/**
 * Express application setup for Campaign Service
 */
import path from 'node:path';
import createError from 'http-errors';
import express, { Request, Response, NextFunction } from 'express';
import cookieParser from 'cookie-parser';
import cors from 'cors';
import helmet from 'helmet';
import http from 'http';

import config from './lib/config';
import { logger } from './lib/logger';
import indexRouter from './routes/index';
import MongooseConnector from './lib/db/connectors/MongooseConnector';
import PrismaConnector from './lib/db/connectors/PrismaConnector';
import { ErrorContextMiddleware } from './lib/middlewares/error.context.middleware';
import { ErrorMiddleware } from './lib/middlewares/error.middleware';

const log = logger(config.logger);
const BASE_PATH = config.api.base_path;

const app = express();
const server = http.createServer(app);
const appDirname = process.cwd();

// Middleware
app.use(cors());
app.use(express.json({ limit: '5mb' }));
app.use(express.urlencoded({ extended: false, limit: '5mb' }));
app.use(cookieParser());
app.use(express.static(path.join(appDirname, 'public')));
app.disable('x-powered-by');
app.use(helmet());

// Error context middleware - must be registered before routes
app.use(ErrorContextMiddleware.captureContext);

// Basic request logger
app.use((req: Request, res: Response, next: NextFunction) => {
  log.info('REQUEST:', {
    method: req.method,
    url: req.headers.host + req.originalUrl,
    origin: req.get('origin') || req.get('Origin'),
    body: req.body,
    queryParams: req.query,
  });
  next();
});

// Routes
app.use(`${BASE_PATH}/`, indexRouter);

// 404 Handler
app.use((req: Request, res: Response, next: NextFunction) => {
  log.info(`404 - Not Found: ${req.method} ${req.originalUrl}`);
  next(createError(404));
});

// Error middleware
app.use(ErrorMiddleware.handleError);

export default async function createApp(skipDbInit: boolean = false): Promise<{ app: express.Application; server: http.Server }> {
  try {
    if (!skipDbInit) {
      await MongooseConnector.initialize();
      PrismaConnector.initialize();
    }
    return { app, server };
  } catch (e) {
    log.error(e);
    return Promise.reject(e);
  }
}
