/**
 * Logger module for the application - copied from core service
 */
import pino, { Logger } from 'pino';

type LoggerConfig = {
  name?: string;
  level?: string;
};

interface LoggerWithTransport {
  logger: Logger;
  transport?: any;
}

class LoggerManager {
  private static instances: LoggerWithTransport[] = [];
  private static isTestEnvironment: boolean = process.env.NODE_ENV === 'test';

  static addInstance(logger: Logger, transport?: any): void {
    if (!this.instances) {
      this.instances = [];
    }
    this.instances.push({ logger, transport });
  }

  static closeAll(): Promise<void> {
    if (!this.instances || this.instances.length === 0) {
      return Promise.resolve();
    }
    const closePromises = this.instances.map(({ logger, transport }) => {
      return new Promise<void>((resolve) => {
        if (logger && typeof (logger as any).flush === 'function') {
          (logger as any).flush(() => {
            if (transport && typeof transport.end === 'function') {
              transport.end();
            }
            resolve();
          });
        } else if (transport && typeof transport.end === 'function') {
          transport.end();
          resolve();
        } else {
          resolve();
        }
      });
    });

    return Promise.all(closePromises).then(() => {
      this.instances = [];
    });
  }

  static isTest(): boolean {
    return this.isTestEnvironment;
  }
}

export const logger = (config: LoggerConfig): Logger => {
  if (LoggerManager.isTest()) {
    const instance = pino({
      name: config.name || 'AppLogger',
      level: config.level || 'info',
    });
    LoggerManager.addInstance(instance);
    return instance;
  }

  const transport = pino.transport({
    target: 'pino-pretty',
    options: {
      colorize: true,
      translateTime: 'yyyy-mm-dd HH:MM:ss.l',
      ignore: 'pid,hostname',
    }
  });

  const instance = pino({
    name: config.name || 'AppLogger',
    level: config.level || 'info',
  }, transport);

  LoggerManager.addInstance(instance, transport);
  return instance;
};

export const closeLoggers = (): Promise<void> => LoggerManager.closeAll();
