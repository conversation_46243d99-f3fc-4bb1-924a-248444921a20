import { Request, Response, NextFunction } from 'express';
import { createClient, SupabaseClient } from '@supabase/supabase-js';
import config from '../../lib/config';
import { logger } from '../../lib/logger';

const log = logger(config.logger);

export interface AuthenticatedRequest extends Request {
  user?: any;
  userId?: string;
  organizationId?: string;
  role?: string;
}

class SupabaseAuthMiddleware {
  private static instance: SupabaseAuthMiddleware;
  private supabase: SupabaseClient | null = null;

  private constructor() {}

  public static getInstance(): SupabaseAuthMiddleware {
    if (!SupabaseAuthMiddleware.instance) {
      SupabaseAuthMiddleware.instance = new SupabaseAuthMiddleware();
    }
    return SupabaseAuthMiddleware.instance;
  }

  public getClient(): SupabaseClient {
    if (!this.supabase) {
      const url = process.env.SUPABASE_URL || config?.supabase?.url;
      const key = process.env.SUPABASE_SERVICE_ROLE_KEY || config?.supabase?.service_role_key;
      if (!url || !key) {
        throw new Error('Supabase configuration is missing');
      }
      this.supabase = createClient(url, key);
    }
    return this.supabase;
  }

  private extractToken(authHeader: string): string | null {
    if (authHeader === 'Bearer') return null;
    const token = authHeader.startsWith('Bearer ')
      ? authHeader.slice(7).trim()
      : authHeader.trim();
    return token && token !== '' ? token : null;
  }

  public async authenticate(req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      // Allow service-to-service auth via header
      const svcKey = req.header('x-service-auth') || req.header('x-service-key');
      const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY || config?.supabase?.service_role_key;
      if (svcKey && serviceRoleKey && svcKey === serviceRoleKey) {
        req.user = { id: 'service', role: 'service' };
        req.userId = 'service';
        req.role = 'service';
        return next();
      }

      const authHeader = req.headers.authorization;
      if (!authHeader) {
        res.status(401).json({ error: 'Authorization header is required', errorCode: 'AUTH_001' });
        return;
      }

      const token = this.extractToken(authHeader);
      if (!token) {
        res.status(401).json({ error: 'Authentication token is required', errorCode: 'AUTH_002' });
        return;
      }

      const { data, error } = await this.getClient().auth.getUser(token);
      if (error || !data.user) {
        log.warn('Authentication failed:', error?.message || 'User not found');
        res.status(401).json({ error: 'Invalid or expired authentication token', errorCode: 'AUTH_003' });
        return;
      }

      const user = data.user;
      req.user = user;
      req.userId = user.id;
      // Extract common metadata org/role if present
      const md: any = user.user_metadata || user.app_metadata || {};
      req.organizationId = md.org_id || md.organization_id;
      req.role = md.role || (md.roles && Array.isArray(md.roles) ? md.roles[0] : undefined);

      next();
    } catch (err) {
      log.error('Authentication error:', err);
      res.status(500).json({ error: 'Internal server error during authentication', errorCode: 'AUTH_004' });
    }
  }
}

export default SupabaseAuthMiddleware;
export const supabaseAuthMiddleware = (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
) => SupabaseAuthMiddleware.getInstance().authenticate(req, res, next);
