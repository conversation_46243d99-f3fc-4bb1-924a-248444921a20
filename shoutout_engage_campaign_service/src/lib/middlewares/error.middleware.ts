/**
 * Enhanced Express Error Middleware - copied from core service (simplified types)
 */
import { Request, Response, NextFunction } from 'express';
import { logger } from '../../lib/logger';

const log = logger({ name: 'ErrorMiddleware' });

export class ErrorMiddleware {
  static handleError = (error: any, req: Request, res: Response, next: NextFunction): void => {
    try {
      const appError = ErrorMiddleware.transformToAppError(error);
      const context = {
        requestId: (req as any).requestId,
        endpoint: req.originalUrl,
        method: req.method,
        timestamp: new Date().toISOString(),
      };

      log.error('Error occurred:', { error: appError, context });

      res.status(appError.statusCode || 500).json({
        error: appError.message || 'Internal server error',
        errorCode: appError.errorCode || 'INTERNAL_ERROR_001',
        details: appError.details,
      });
    } catch (middlewareError) {
      log.error('Error in error middleware:', middlewareError);
      res.status(500).json({ error: 'Internal server error', errorCode: 'INTERNAL_ERROR_001' });
    }
  };

  static transformToAppError(error: any): any {
    if (error && error.statusCode) return error;
    if (error && (error.name === 'MongoError' || error.name === 'MongoServerError')) {
      return { statusCode: 500, message: 'Database operation failed', errorCode: 'DB_001' };
    }
    if (error && error.name === 'PrismaClientKnownRequestError') {
      return { statusCode: 500, message: 'Database operation failed', errorCode: 'DB_002' };
    }
    return { statusCode: 500, message: 'An unexpected error occurred', errorCode: 'INTERNAL_001' };
  }
}
