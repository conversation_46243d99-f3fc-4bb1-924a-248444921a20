/**
 * Error Context Middleware - copied from core service
 */
import { Request, Response, NextFunction } from 'express';
import { v4 as uuidv4 } from 'uuid';

declare global {
  namespace Express {
    interface Request {
      requestId: string;
      errorContext?: any;
      user?: {
        id: string;
        organizationId?: string;
        [key: string]: any;
      };
    }
  }
}

export class ErrorContextMiddleware {
  static captureContext = (req: Request, res: Response, next: NextFunction): void => {
    try {
      if (!req.requestId) {
        req.requestId = uuidv4();
      }
      req.errorContext = { requestId: req.requestId, timestamp: new Date() };
      res.setHeader('X-Request-ID', req.requestId);
      next();
    } catch (error) {
      console.error('Failed to capture error context:', error);
      if (!req.requestId) {
        req.requestId = uuidv4();
      }
      next();
    }
  };
}

export const captureErrorContext = ErrorContextMiddleware.captureContext;
