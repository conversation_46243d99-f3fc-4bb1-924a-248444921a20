'use strict';
import path from 'node:path';
import * as yaml_config from 'node-yaml-config';
import fs from 'fs';
import { getSafeDirname } from '@shoutout/engage-utils';

const { __dirname: currentDir } = getSafeDirname();

function findProjectRoot(startDir: string): string {
  let currentDir = startDir;
  while (currentDir !== path.dirname(currentDir)) {
    const packageJsonPath = path.join(currentDir, 'package.json');
    if (fs.existsSync(packageJsonPath)) {
      return currentDir;
    }
    currentDir = path.dirname(currentDir);
  }
  return process.cwd();
}

const projectRoot = findProjectRoot(currentDir);
const fileName = 'common.yml';
const configPath = path.resolve(projectRoot, 'config', fileName);

if (!fs.existsSync(configPath)) {
  throw new Error(`Configuration file not found at: ${configPath}`);
}

const environment = process.env.NODE_ENV || 'development';
const rawConfig = yaml_config.load(configPath);
const config = rawConfig[environment] || rawConfig.defaults || rawConfig;

if (!config || typeof config !== 'object') {
  throw new Error('Invalid configuration loaded from YAML file');
}

if (!config.api || !config.api.base_path) {
  throw new Error('Missing required configuration: api.base_path');
}

export default config as any;
