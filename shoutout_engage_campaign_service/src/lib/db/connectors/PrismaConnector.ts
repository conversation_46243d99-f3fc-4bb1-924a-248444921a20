import { PrismaClient } from '@prisma/client';
import { logger } from '../../../lib/logger';
import config from '../../../lib/config';

const log = logger(config.logger);

class PrismaConnector {
  private static prisma: PrismaClient;

  static initialize(): PrismaClient {
    if (!this.prisma) {
      this.prisma = new PrismaClient();
      log.info('🟢 Prisma Client initialized');
    }
    return this.prisma;
  }

  static getClient(): PrismaClient {
    if (!this.prisma) {
      throw new Error('❌ Prisma client not initialized. Call `PrismaConnector.initialize()` first.');
    }
    return this.prisma;
  }

  static async close(): Promise<void> {
    if (this.prisma) {
      await this.prisma.$disconnect();
      log.info('🔴 Prisma Client disconnected');
    }
  }
}

export default PrismaConnector;
