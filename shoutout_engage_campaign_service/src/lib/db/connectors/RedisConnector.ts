import { createClient, RedisClientType } from 'redis';
import dotenv from 'dotenv';
import config from '../../../lib/config';
import { logger } from '../../../lib/logger';

dotenv.config();
const log = logger(config.logger);

class RedisConnector {
  private static baseConfig = {
    host: process.env.REDIS_HOST || config?.database?.redis?.host || 'localhost',
    port: Number(process.env.REDIS_PORT || config?.database?.redis?.port || 6379),
    password: process.env.REDIS_PASSWORD && process.env.REDIS_PASSWORD.trim() !== '' ? process.env.REDIS_PASSWORD : undefined,
  };

  static getConfig(format: 'standard' | 'bull' = 'standard') {
    if (format === 'bull') {
      return {
        ...this.baseConfig,
        maxRetriesPerRequest: null,
        retryDelayOnFailover: 100,
        enableReadyCheck: false,
        lazyConnect: true,
        maxLoadingTimeout: 0,
      };
    }
    return {
      socket: {
        host: this.baseConfig.host,
        port: this.baseConfig.port,
        connectTimeout: 10000,
        lazyConnect: true,
      },
      password: this.baseConfig.password,
    };
  }

  static async scanAndDelete(pattern: string): Promise<void> {
    try {
      const redisClient = await getClient();
      let cursor: string = '0';
      do {
        const reply = await redisClient.scan(cursor, { MATCH: pattern, COUNT: 1000 });
        cursor = String(reply.cursor);
        const keys = reply.keys;
        if (keys.length > 0) await redisClient.del(keys);
      } while (cursor !== '0');
    } catch (e) {
      log.error(e);
    }
  }

  static async closeConnection(): Promise<void> {
    try {
      if (client) {
        await client.quit();
        client = null;
      }
    } catch (err) {
      log.error('Error closing Redis connection', err);
      throw err;
    }
  }
}

const redisClientOptions = RedisConnector.getConfig('standard');
let client: RedisClientType | null = null;
let isConnecting = false;

async function getClient(): Promise<RedisClientType> {
  if (client && client.isReady) return client;
  if (isConnecting) {
    while (isConnecting) await new Promise((r) => setTimeout(r, 100));
    if (client && client.isReady) return client;
  }
  isConnecting = true;
  try {
    if (!client) {
      client = createClient(redisClientOptions);
      client.on('error', (err: any) => log.error('Redis Client Error', err));
    }
    if (!client.isReady) {
      await client.connect();
      log.info('Redis client connected successfully');
    }
    return client;
  } catch (err) {
    log.error('Failed to connect to Redis:', err);
    throw err;
  } finally {
    isConnecting = false;
  }
}

export default RedisConnector;
