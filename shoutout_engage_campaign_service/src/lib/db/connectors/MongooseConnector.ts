import mongoose, { Connection } from 'mongoose';
import config from '../../../lib/config';
import { logger } from '../../../lib/logger';

const log = logger(config.logger);

class MongooseConnector {
  private static connection: Connection | null = null;

  static async initialize(): Promise<typeof mongoose> {
    if (this.connection) {
      log.info('🟢 MongoDB already connected');
      return mongoose;
    }

    const envUri = process.env.MONGODB_URI || process.env.MONGO_URL || config?.database?.mongodb?.uri || 'mongodb://localhost:27017/shoutout_engage_campaign';
    const masked = envUri.replace(/\/\/[^:]+:[^@]+@/, '//***:***@');
    log.info(`🔄 Attempting to connect to MongoDB: ${masked}`);

    try {
      const conn = await mongoose.connect(envUri, { maxPoolSize: 10 });
      this.connection = mongoose.connection;
      this.connection.on('error', (err) => log.error('🔴 MongoDB connection error:', err));
      this.connection.once('open', () => log.info('🟢 MongoDB connected successfully'));
      this.connection.on('disconnected', () => log.warn('⚠️ MongoDB disconnected'));
      return conn;
    } catch (err) {
      log.error('🔴 MongoDB connection failed:', err);
      throw err;
    }
  }

  static getConnection(): Connection | null {
    return this.connection;
  }

  static async close(): Promise<void> {
    try {
      if (this.connection) {
        await this.connection.close();
        log.info('🔴 MongoDB connection closed');
        this.connection = null;
      }
    } catch (err) {
      log.error('Error closing MongoDB connection:', err);
      throw err;
    }
  }
}

export default MongooseConnector;
