#!/usr/bin/env node

import * as dotenv from 'dotenv';
import * as path from 'path';
import { fileURLToPath } from 'url';

dotenv.config();

import appPromise from '../src/app';
import debugLib from 'debug';
import config from '../src/lib/config';
import { logger } from '../src/lib/logger';
import { createTerminus } from '@godaddy/terminus';
import RedisConnector from '../src/lib/db/connectors/RedisConnector';
import MongooseConnector from '../src/lib/db/connectors/MongooseConnector';
import PrismaConnector from '../src/lib/db/connectors/PrismaConnector';
import { Worker, isMainThread } from 'worker_threads';
import http from 'http';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const debug = debugLib('campaign:server');
const log = logger(config.logger);

let isShuttingDown = false;
let worker: Worker | null = null;

if (isMainThread) {
  (async () => {
    const { app, server } = await appPromise();

    const port = normalizePort(process.env.PORT || String(config.api.port || 3001));
    app.set('port', port);

    const healthCheckPath = `${config.api.base_path}/healthcheck`;
    const healthChecks: Record<string, () => Promise<void>> = {
      [healthCheckPath]: onHealthCheck,
    };

    createTerminus(server as unknown as http.Server, {
      signals: ['SIGINT', 'SIGTERM', 'SIGUSR2'],
      healthChecks,
      onSignal,
    });

    server.listen(port);
    server.on('error', onError);
    server.on('listening', onListening);

    async function onSignal(): Promise<void> {
      if (isShuttingDown) {
        log.info('Shutdown already in progress, ignoring additional signal');
        return;
      }
      isShuttingDown = true;
      log.info('Server is starting cleanup');

      try {
        await shutdownWorker();
      } catch (error) {
        log.error('Error during worker shutdown:', error instanceof Error ? error.message : String(error));
      }

      log.info('cleaning redis cache...');
      await RedisConnector.scanAndDelete('users:*');
      await RedisConnector.scanAndDelete('groups:*');
      await RedisConnector.scanAndDelete('modules:*');

      log.info('closing redis connection...');
      await RedisConnector.closeConnection();
      log.info('closing mongo connection...');
      await MongooseConnector.close();
      log.info('closing postgres connection...');
      await PrismaConnector.close();
    }

    async function onHealthCheck(): Promise<void> {
      log.debug(`health check on ${new Date().toISOString()}`);
    }

    function normalizePort(val: string): number | string | false {
      const port = parseInt(val, 10);
      if (isNaN(port)) return val;
      if (port >= 0) return port;
      return false;
    }

    function onError(error: NodeJS.ErrnoException): void {
      if (error.syscall !== 'listen') throw error;
      const bind = typeof port === 'string' ? 'Pipe ' + port : 'Port ' + port;
      switch (error.code) {
        case 'EACCES':
          console.error(`${bind} requires elevated privileges`);
          process.exit(1);
        case 'EADDRINUSE':
          console.error(`${bind} is already in use`);
          process.exit(1);
        default:
          throw error;
      }
    }

    function onListening(): void {
      const addr = (server as http.Server).address();
      if (!addr) return;
      const bind = typeof addr === 'string' ? 'pipe ' + addr : 'port ' + addr.port;
      debug(`Listening on ${bind}`);
      log.info(`server listening on ${bind}`);
    }

    async function shutdownWorker(): Promise<void> {
      if (!worker) {
        log.info('No worker thread to terminate');
        return;
      }
      log.info('Terminating worker thread...');
      try {
        worker.postMessage({ type: 'shutdown' });
        const shutdownPromise = new Promise<void>((resolve) => {
          let handlerCalled = false;
          const messageHandler = (msg: any) => {
            if (handlerCalled) return;
            if (msg.type === 'processors-stopped' || msg.type === 'processors-shutdown-error') {
              handlerCalled = true;
              worker?.removeListener('message', messageHandler);
              resolve();
            }
          };
          worker?.once('exit', () => {
            if (!handlerCalled) {
              handlerCalled = true;
              resolve();
            }
          });
          worker?.on('message', messageHandler);
          setTimeout(() => {
            if (!handlerCalled) {
              handlerCalled = true;
              worker?.removeListener('message', messageHandler);
              resolve();
            }
          }, 5000);
        });
        await shutdownPromise;
        if (worker) {
          await worker.terminate();
          log.info('Worker thread terminated');
          worker = null;
        }
      } catch (error) {
        log.error('Error terminating worker:', error instanceof Error ? error.message : String(error));
        if (worker) {
          try {
            await worker.terminate();
            log.info('Worker thread force terminated');
            worker = null;
          } catch (terminateError) {
            log.error('Failed to force terminate worker:', terminateError);
          }
        }
      }
    }
  })();

  try {
    const workerPath = path.join(__dirname, '../src/workers/main.ts');
    worker = new Worker(workerPath);
    worker.on('message', (msg) => {
      if (!msg || typeof msg !== 'object' || !msg.type) return;
      if (msg.type === 'heartbeat') log.debug('Worker heartbeat:', msg.timestamp);
    });
    worker.on('error', (error) => log.error('Worker thread error:', error));
    worker.on('exit', (code) => {
      if (code !== 0) log.error(`Worker stopped with exit code ${code}`);
      else log.info('Worker thread exited gracefully');
    });
    log.info('Worker thread started successfully');
  } catch (error) {
    log.error('Failed to start worker thread:', error);
  }
} else {
  log.info('Running on worker thread');
}
