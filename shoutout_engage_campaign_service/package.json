{"name": "shoutout-engage-campaign-service", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "PORT=3001 nodemon --exec 'node --import ./register.mjs --no-deprecation' ./bin/www.ts", "build": "tsc -p .", "start": "node dist/bin/www.js", "type-check": "tsc -p . --noEmit", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev", "dev-campaign:www": "PORT=3001 nodemon --exec 'node --import ./register.mjs --no-deprecation' ./bin/www.ts"}, "dependencies": {"@godaddy/terminus": "^4.12.1", "@prisma/client": "^6.12.0", "@supabase/supabase-js": "^2.0.0", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "dotenv": "^16.0.0", "express": "^4.18.0", "helmet": "^8.1.0", "http-errors": "^2.0.0", "mongoose": "^8.16.3", "node-yaml-config": "^1.0.0", "pino": "^9.7.0", "pino-pretty": "^13.0.0", "redis": "^5.6.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "uuid": "^11.1.0"}, "devDependencies": {"cross-env": "^7.0.3", "nodemon": "^2.0.0", "prisma": "^6.12.0", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "^5.6.3"}}