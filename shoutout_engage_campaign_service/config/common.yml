defaults: &defaults
  api:
    port: 3001
    base_path: /api/campaign/v1
  logger:
    name: Shoutout Engage Campaign Service
    level: info
  supabase:
    url: ${SUPABASE_URL}
    service_role_key: ${SUPABASE_SERVICE_ROLE_KEY}
  database:
    mongodb:
      uri: ${MONGODB_URI}
      connection_timeout: 10000
    redis:
      host: ${REDIS_HOST}
      port: ${REDIS_PORT}
      connection_timeout: 5000
    postgresql:
      url: ${DATABASE_URL}
      connection_timeout: 10000

development:
  <<: *defaults

test:
  <<: *defaults
  database:
    mongodb:
      uri: mongodb://localhost:27017/shoutout_engage_campaign_test
    postgresql:
      url: postgresql://username:password@localhost:5432/shoutout_engage_campaign_test
