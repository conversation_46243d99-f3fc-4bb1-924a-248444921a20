{"compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "Node", "outDir": "dist", "rootDir": ".", "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "strict": true, "skipLibCheck": true, "resolveJsonModule": true, "types": ["node"], "baseUrl": ".", "paths": {"@shoutout/engage-utils": ["../shoutout_engage_utils/src"], "*": ["./src/*"]}}, "include": ["src", "bin", "prisma"], "exclude": ["node_modules", "dist"]}