import { OAS3Options } from 'swagger-jsdoc';
import config from '../config/index.js';

class Swagger {
    static getOptions(): OAS3Options {
        return {
            definition: {
                openapi: '3.0.0',
                info: {
                    title: 'ShoutOUT Engage Payment Service',
                    version: '1.0.0',
                    description:
                        'Payment processing microservice for ShoutOut Engage platform. Handles subscriptions, payments, and billing through Stripe integration.',
                    contact: {
                        name: 'ShoutOUT',
                        url: 'https://getshoutout.com/',
                        email: '<EMAIL>',
                    },
                },
                servers: [
                    {
                        url: process.env.SWAGGER_SERVER_URL || `http://localhost:3003${config.api.base_path}`,
                        description: 'Payment Service API Server',
                    },
                ],
                components: {
                    securitySchemes: {
                        BearerAuth: {
                            type: 'http',
                            scheme: 'bearer',
                            bearerFormat: 'JWT',
                            description: 'Supabase JWT token for user authentication',
                        },
                        ApiKey: {
                            type: 'apiKey',
                            in: 'header',
                            name: 'x-api-key',
                            description: 'API key for service-to-service communication',
                        },
                    },
                    schemas: {
                        PaymentRequest: {
                            type: 'object',
                            required: ['amount', 'currency', 'paymentMethodId'],
                            properties: {
                                amount: {
                                    type: 'number',
                                    description: 'Payment amount in cents',
                                },
                                currency: {
                                    type: 'string',
                                    description: 'Currency code (e.g., USD, EUR)',
                                    example: 'USD',
                                },
                                paymentMethodId: {
                                    type: 'string',
                                    description: 'Stripe payment method ID',
                                },
                                description: {
                                    type: 'string',
                                    description: 'Payment description',
                                },
                            },
                        },
                        SubscriptionRequest: {
                            type: 'object',
                            required: ['priceId', 'paymentMethodId'],
                            properties: {
                                priceId: {
                                    type: 'string',
                                    description: 'Stripe price ID',
                                },
                                paymentMethodId: {
                                    type: 'string',
                                    description: 'Stripe payment method ID',
                                },
                                trialPeriodDays: {
                                    type: 'number',
                                    description: 'Trial period in days',
                                },
                            },
                        },
                        PaymentResponse: {
                            type: 'object',
                            properties: {
                                success: {
                                    type: 'boolean',
                                },
                                paymentId: {
                                    type: 'string',
                                },
                                clientSecret: {
                                    type: 'string',
                                },
                                status: {
                                    type: 'string',
                                    enum: ['requires_payment_method', 'requires_confirmation', 'requires_action', 'processing', 'succeeded', 'canceled'],
                                },
                                amount: {
                                    type: 'number',
                                },
                                currency: {
                                    type: 'string',
                                },
                            },
                        },
                        SubscriptionResponse: {
                            type: 'object',
                            properties: {
                                success: {
                                    type: 'boolean',
                                },
                                subscriptionId: {
                                    type: 'string',
                                },
                                clientSecret: {
                                    type: 'string',
                                },
                                status: {
                                    type: 'string',
                                    enum: ['incomplete', 'incomplete_expired', 'trialing', 'active', 'past_due', 'canceled', 'unpaid'],
                                },
                                currentPeriodStart: {
                                    type: 'string',
                                    format: 'date-time',
                                },
                                currentPeriodEnd: {
                                    type: 'string',
                                    format: 'date-time',
                                },
                            },
                        },
                        InvoiceResponse: {
                            type: 'object',
                            properties: {
                                success: {
                                    type: 'boolean',
                                },
                                invoiceId: {
                                    type: 'string',
                                },
                                amount: {
                                    type: 'number',
                                },
                                currency: {
                                    type: 'string',
                                },
                                status: {
                                    type: 'string',
                                    enum: ['draft', 'open', 'paid', 'void', 'uncollectible'],
                                },
                                dueDate: {
                                    type: 'string',
                                    format: 'date-time',
                                },
                                hostedInvoiceUrl: {
                                    type: 'string',
                                },
                            },
                        },
                        ErrorResponse: {
                            type: 'object',
                            properties: {
                                success: {
                                    type: 'boolean',
                                    example: false,
                                },
                                error: {
                                    type: 'object',
                                    properties: {
                                        code: {
                                            type: 'string',
                                        },
                                        message: {
                                            type: 'string',
                                        },
                                        requestId: {
                                            type: 'string',
                                        },
                                    },
                                },
                            },
                        },
                    },
                },
                security: [
                    {
                        BearerAuth: [],
                    },
                ],
            },
            apis: ['./src/routes/*.ts'],
        };
    }
}

export default Swagger;