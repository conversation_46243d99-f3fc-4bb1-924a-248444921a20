import mongoose, { Connection } from 'mongoose';
import config from '../../config/index.js';
import { logger } from '../../utils/logger.js';

const log = logger(config.logger);

class MongooseConnector {
    private static connection: Connection;

    /**
     * Initialize the main MongoDB connection
     */
    static async initialize(): Promise<typeof mongoose> {
        if (this.connection) {
            log.info('🟢 MongoDB already connected');
            return mongoose;
        }

        const mongoUrl = config.database.uri;
        log.info(`🔄 Attempting to connect to MongoDB: ${mongoUrl.replace(/\/\/[^:]+:[^@]+@/, '//***:***@')}`);

        try {
            const conn = await mongoose.connect(mongoUrl, {
                maxPoolSize: config.database.options?.maxPoolSize || 10,
                serverSelectionTimeoutMS: config.database.options?.serverSelectionTimeoutMS || 5000,
                socketTimeoutMS: config.database.options?.socketTimeoutMS || 45000,
            });

            this.connection = mongoose.connection;

            this.connection.on('error', (err) => {
                log.error('🔴 MongoDB connection error:', err);
            });

            this.connection.once('open', () => {
                log.info('🟢 MongoDB connected successfully');
            });

            this.connection.on('disconnected', () => {
                log.warn('⚠️ MongoDB disconnected');
            });

            log.info('🟢 MongoDB connection initialized for Payment Service');
            return conn;
        } catch (err) {
            log.error('🔴 MongoDB connection failed:', err);
            return Promise.reject(err);
        }
    }

    /**
     * Return the connection instance
     */
    static getConnection(): Connection | undefined {
        return this.connection;
    }

    /**
     * Check if MongoDB is connected
     */
    static isConnected(): boolean {
        return this.connection && this.connection.readyState === 1;
    }

    /**
     * Disconnect from MongoDB
     */
    static async disconnect(): Promise<void> {
        try {
            if (this.connection) {
                await mongoose.disconnect();
                log.info('🟡 MongoDB disconnected for Payment Service');
            }
        } catch (err) {
            log.error('🔴 Error disconnecting from MongoDB:', err);
            throw err;
        }
    }

    /**
     * Close the MongoDB connection
     */
    static async close(): Promise<void> {
        return this.disconnect();
    }
}

export default MongooseConnector;