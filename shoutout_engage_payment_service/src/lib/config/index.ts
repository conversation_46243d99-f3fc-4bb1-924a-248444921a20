/**
 * Configuration module for Payment Service
 * 
 * This module loads configuration from environment variables and provides
 * a centralized configuration object for the entire application.
 */

import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config();

export interface DatabaseConfig {
  uri: string;
  options?: {
    maxPoolSize?: number;
    serverSelectionTimeoutMS?: number;
    socketTimeoutMS?: number;
  };
}

export interface RedisConfig {
  host: string;
  port: number;
  password?: string;
  db: number;
}

export interface SupabaseConfig {
  url: string;
  anonKey: string;
  serviceRoleKey: string;
}

export interface ApiConfig {
  base_path: string;
  version: string;
}

export interface LoggerConfig {
  name: string;
  level: string;
}

export interface RateLimitConfig {
  windowMs: number;
  maxRequests: number;
}

export interface PaymentServiceConfig {
  database: DatabaseConfig;
  redis: RedisConfig;
  supabase: SupabaseConfig;
  api: ApiConfig;
  logger: LoggerConfig;
  rateLimit: RateLimitConfig;
  jwt: {
    secret: string;
    expiresIn: string;
  };
  stripe: {
    publishableKey?: string;
    secretKey?: string;
    webhookSecret?: string;
    apiVersion: string;
  };
  webhook: {
    secret: string;
    baseUrl: string;
  };
  upload: {
    maxFileSize: number;
    path: string;
  };
  billing: {
    defaultCurrency: string;
    billingCycleDay: number;
    invoiceDueDays: number;
    trialPeriodDays: number;
  };
}

const config: PaymentServiceConfig = {
  database: {
    uri: process.env.MONGODB_URI || 'mongodb://localhost:27017/shoutout_engage_payment',
    options: {
      maxPoolSize: 10,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000,
    }
  },
  redis: {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || '6379'),
    password: process.env.REDIS_PASSWORD || undefined,
    db: parseInt(process.env.REDIS_DB || '0'),
  },
  supabase: {
    url: process.env.SUPABASE_URL || '',
    anonKey: process.env.SUPABASE_ANON_KEY || '',
    serviceRoleKey: process.env.SUPABASE_SERVICE_ROLE_KEY || '',
  },
  api: {
    base_path: process.env.BASE_PATH || '/api/payment/v1',
    version: '1.0.0',
  },
  logger: {
    name: 'PaymentService',
    level: process.env.LOG_LEVEL || 'info',
  },
  rateLimit: {
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000'), // 15 minutes
    maxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100'),
  },
  jwt: {
    secret: process.env.JWT_SECRET || 'your-jwt-secret',
    expiresIn: process.env.JWT_EXPIRES_IN || '24h',
  },
  stripe: {
    publishableKey: process.env.STRIPE_PUBLISHABLE_KEY,
    secretKey: process.env.STRIPE_SECRET_KEY,
    webhookSecret: process.env.STRIPE_WEBHOOK_SECRET,
    apiVersion: process.env.STRIPE_API_VERSION || '2023-10-16',
  },
  webhook: {
    secret: process.env.WEBHOOK_SECRET || 'your-webhook-secret',
    baseUrl: process.env.BASE_WEBHOOK_URL || 'http://localhost:3003/api/payment/v1/webhooks',
  },
  upload: {
    maxFileSize: parseInt(process.env.MAX_FILE_SIZE || '10485760'), // 10MB
    path: process.env.UPLOAD_PATH || './uploads',
  },
  billing: {
    defaultCurrency: process.env.DEFAULT_CURRENCY || 'USD',
    billingCycleDay: parseInt(process.env.BILLING_CYCLE_DAY || '1'),
    invoiceDueDays: parseInt(process.env.INVOICE_DUE_DAYS || '7'),
    trialPeriodDays: parseInt(process.env.TRIAL_PERIOD_DAYS || '14'),
  },
};

export default config;