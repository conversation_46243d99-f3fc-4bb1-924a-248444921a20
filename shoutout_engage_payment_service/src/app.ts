/**
 * Express application setup for Payment Service
 */

import path from 'node:path';
import createError from 'http-errors';
import express, { Request, Response, NextFunction } from 'express';
import cookieParser from 'cookie-parser';
import cors from 'cors';
import helmet from 'helmet';
import http from 'http';
import rateLimit from 'express-rate-limit';

import swaggerUi from 'swagger-ui-express';
import swaggerJsdoc from 'swagger-jsdoc';

import config from './lib/config/index.js';
import { logger } from './lib/utils/logger.js';

import indexRouter from './routes/index.js';
import swagger from './lib/swagger/swagger.config.js';
import MongooseConnector from './lib/db/connectors/MongooseConnector.js';
import RedisConnector from './lib/db/connectors/RedisConnector.js';

import { lazyLoadRoute } from '@shoutout/engage-utils/dist/lazyRouterLoader.js';
const log = logger(config.logger);

import { authenticateRequest, authenticateService } from './middleware/auth.middleware.js';
import { ErrorContextMiddleware } from './middleware/error.context.middleware.js';
import { ErrorMiddleware } from './middleware/error.middleware.js';

// Payment service specific routes
const paymentRoute = lazyLoadRoute(() => import('./routes/payment.routes.js'));
const subscriptionRoute = lazyLoadRoute(() => import('./routes/subscription.routes.js'));
const invoiceRoute = lazyLoadRoute(() => import('./routes/invoice.routes.js'));
const webhookRoute = lazyLoadRoute(() => import('./routes/webhook.routes.js'));

const BASE_PATH = config.api.base_path;

const app = express();
const server = http.createServer(app);

// Use a simple path resolution that works in both runtime and test environments
const appDirname = process.cwd();

// Rate limiting
const limiter = rateLimit({
    windowMs: config.rateLimit.windowMs,
    max: config.rateLimit.maxRequests,
    message: {
        error: 'Too many requests from this IP, please try again later.',
        code: 'RATE_LIMIT_EXCEEDED'
    },
    standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
    legacyHeaders: false, // Disable the `X-RateLimit-*` headers
});

// Middleware
app.use(cors({ 
    exposedHeaders: ['x-skip', 'x-limit', 'x-total', 'RateLimit-Limit', 'RateLimit-Remaining', 'RateLimit-Reset'] 
}));
app.use(express.json({ limit: '10mb' })); // Larger limit for file uploads
app.use(express.urlencoded({ extended: false, limit: '10mb' }));
app.use(cookieParser());
app.use(express.static(path.join(appDirname, 'public')));
app.disable('x-powered-by');
app.use(helmet());
app.use(limiter);

// Swagger
app.use(`${BASE_PATH}/docs`, swaggerUi.serve, swaggerUi.setup(swaggerJsdoc(swagger.getOptions())));

// Error context middleware - must be registered before route handlers
app.use(ErrorContextMiddleware.captureContext);

// Logging
app.use((req: Request, res: Response, next: NextFunction) => {
    log.info('REQUEST:', {
        method: req.method,
        url: req.headers.host + req.originalUrl,
        origin: req.get('origin') || req.get('Origin'),
        body: req.body,
        queryParams: req.query,
        userAgent: req.get('User-Agent'),
    });
    next();
});

// Mount routers
app.use(`${BASE_PATH}/`, indexRouter);

const registerRoute = (
    path: string,
    routeModule: any,
    middleware: any = authenticateRequest
) => {
    app.use(`${BASE_PATH}/${path}`, middleware, routeModule);
};

// Public routes (no authentication required)
const registerPublicRoute = (path: string, routeModule: any) => {
    app.use(`${BASE_PATH}/${path}`, routeModule);
};

// Service routes (service-to-service authentication)
const registerServiceRoute = (path: string, routeModule: any) => {
    app.use(`${BASE_PATH}/${path}`, authenticateService, routeModule);
};

// ⛳️ Payment Service Routes

// Protected routes (require user authentication)
registerRoute('payments', paymentRoute);
registerRoute('subscriptions', subscriptionRoute);
registerRoute('invoices', invoiceRoute);

// Public webhook routes (Stripe calls these)
registerPublicRoute('webhooks', webhookRoute);

// Public health check route
registerPublicRoute('health', (req: Request, res: Response) => {
    res.json({ 
        status: 'OK', 
        service: 'payment-service',
        timestamp: new Date().toISOString(),
        version: '1.0.0',
        uptime: process.uptime(),
        environment: process.env.NODE_ENV || 'development'
    });
});

// Service health check with detailed status
registerServiceRoute('health/detailed', async (req: Request, res: Response) => {
    try {
        // Check database connections
        const mongoStatus = MongooseConnector.isConnected() ? 'connected' : 'disconnected';
        const redisStatus = await RedisConnector.ping() ? 'connected' : 'disconnected';

        const healthStatus = {
            status: mongoStatus === 'connected' && redisStatus === 'connected' ? 'OK' : 'DEGRADED',
            service: 'payment-service',
            timestamp: new Date().toISOString(),
            version: '1.0.0',
            uptime: process.uptime(),
            environment: process.env.NODE_ENV || 'development',
            dependencies: {
                mongodb: mongoStatus,
                redis: redisStatus,
                stripe: config.stripe.secretKey ? 'configured' : 'not-configured'
            }
        };

        const statusCode = healthStatus.status === 'OK' ? 200 : 503;
        res.status(statusCode).json(healthStatus);
    } catch (error) {
        log.error('Health check failed:', error);
        res.status(503).json({
            status: 'ERROR',
            service: 'payment-service',
            timestamp: new Date().toISOString(),
            error: 'Health check failed'
        });
    }
});

// 404 Handler
app.use((req: Request, res: Response, next: NextFunction) => {
    log.info(`404 - Not Found: ${req.method} ${req.originalUrl}`);
    next(createError(404));
});

// Enhanced error middleware - must be registered as the final error handler
app.use(ErrorMiddleware.handleError);

export default async function createApp(skipDbInit: boolean = false): Promise<{ app: express.Application; server: http.Server }> {
    try {
        if (!skipDbInit) {
            // Initialize database connections
            await Promise.all([
                MongooseConnector.initialize(),
                RedisConnector.initialize()
            ]);
            
            log.info('Database connections initialized successfully');
        }
        return { app, server };
    } catch (e) {
        log.error('Failed to initialize application:', e);
        return Promise.reject(e);
    }
}