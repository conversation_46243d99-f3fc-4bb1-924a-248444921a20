# Server Configuration
NODE_ENV=development
PORT=3003
BASE_PATH=/api/payment/v1

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/shoutout_engage_payment
MONGODB_TEST_URI=mongodb://localhost:27017/shoutout_engage_payment_test

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# Supabase Configuration
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# JWT Configuration
JWT_SECRET=your-jwt-secret
JWT_EXPIRES_IN=24h

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Stripe Configuration
STRIPE_PUBLISHABLE_KEY=pk_test_your_publishable_key
STRIPE_SECRET_KEY=sk_test_your_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret
STRIPE_API_VERSION=2023-10-16

# File Upload
MAX_FILE_SIZE=10485760
UPLOAD_PATH=./uploads

# Logging
LOG_LEVEL=info
LOG_FILE=./logs/payment-service.log

# External Services
CAMPAIGN_SERVICE_URL=http://localhost:3001
MESSAGE_SERVICE_URL=http://localhost:3002
CORE_SERVICE_URL=http://localhost:3000

# Queue Configuration
QUEUE_REDIS_HOST=localhost
QUEUE_REDIS_PORT=6379
QUEUE_REDIS_DB=2

# Internal API Key for service-to-service communication
INTERNAL_API_KEY=your-internal-api-key

# Billing Configuration
DEFAULT_CURRENCY=USD
BILLING_CYCLE_DAY=1
INVOICE_DUE_DAYS=7
TRIAL_PERIOD_DAYS=14

# Webhook Configuration
WEBHOOK_SECRET=your-webhook-secret
BASE_WEBHOOK_URL=https://your-domain.com/api/payment/v1/webhooks