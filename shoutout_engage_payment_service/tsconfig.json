{"compilerOptions": {"target": "ES2020", "module": "ESNext", "lib": ["ES2020"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "moduleResolution": "node", "baseUrl": "./src", "paths": {"@/*": ["*"], "@/types/*": ["types/*"], "@/lib/*": ["lib/*"], "@/services/*": ["services/*"], "@/controllers/*": ["controllers/*"], "@/middleware/*": ["middleware/*"], "@/routes/*": ["routes/*"]}}, "include": ["src/**/*", "bin/**/*"], "exclude": ["node_modules", "dist", "tests"]}