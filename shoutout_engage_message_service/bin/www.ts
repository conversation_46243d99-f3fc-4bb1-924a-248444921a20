#!/usr/bin/env node

/**
 * Load environment variables first
 */
import * as dotenv from 'dotenv';
import * as path from 'path';
import { fileURLToPath } from 'url';

const wwwFilename = fileURLToPath(import.meta.url);
const wwwDirname = path.dirname(wwwFilename);

// Load environment variables before any other imports
dotenv.config({ path: path.resolve(wwwDirname, '../.env') });

/**
 * Module dependencies.
 */

import appPromise from '../src/app.js';
import debugLib from 'debug';
import config from '../src/lib/config/index.js';
import {logger} from '../src/lib/utils/logger.js';

import { createTerminus } from '@godaddy/terminus';
import RedisConnector from '../src/lib/db/connectors/RedisConnector.js';
import MongooseConnector from '../src/lib/db/connectors/MongooseConnector.js';
import { Worker, isMainThread } from 'worker_threads';

const debug = debugLib('message-service:server');
const log = logger({ name: 'MessageService', level: config.logger?.level || 'info' });

// Track shutdown state
let isShuttingDown = false;
let worker: Worker | null = null;

if (isMainThread) {
    (async () => {
        const { app, server } = await appPromise();

        const port = normalizePort(process.env.PORT || '3002');
        app.set('port', port);

        const healthCheckPath = `${config.api?.base_path || '/api/message/v1'}/health`;
        const healthChecks: Record<string, () => Promise<void>> = {
            [healthCheckPath]: onHealthCheck,
        };

        // Gracefully shut down on termination signals
        createTerminus(server, {
            signals: ['SIGINT', 'SIGTERM', 'SIGUSR2'],
            healthChecks,
            onSignal,
        });

        server.listen(port);
        server.on('error', onError);
        server.on('listening', onListening);

        async function onSignal(): Promise<void> {
            // Prevent multiple concurrent shutdowns
            if (isShuttingDown) {
                log.info('Shutdown already in progress, ignoring additional signal');
                return;
            }

            isShuttingDown = true;
            log.info('Message service is starting cleanup');

            try {
                // Terminate worker thread first
                await shutdownWorker();
            } catch (error) {
                log.error('Error during worker shutdown:', error instanceof Error ? error.message : String(error));
                // Continue with shutdown even if worker shutdown fails
            }

            /**
             * Shutdown worker thread gracefully
             */
            async function shutdownWorker(): Promise<void> {
                if (!worker) {
                    log.info('No worker thread to terminate');
                    return;
                }

                log.info('Terminating message processing worker thread...');

                try {
                    // Send shutdown message to worker
                    worker.postMessage({ type: 'shutdown' });

                    // Create a proper shutdown promise with timeout
                    const shutdownPromise = new Promise<void>((resolve) => {
                        // Track if the handler has already been called
                        let handlerCalled = false;

                        const messageHandler = (msg: any) => {
                            // Prevent multiple handler calls
                            if (handlerCalled) return;

                            if (msg.type === 'processors-stopped' || msg.type === 'processors-shutdown-error') {
                                handlerCalled = true;

                                if (msg.type === 'processors-stopped') {
                                    log.info('Message processors stopped successfully');
                                } else {
                                    log.error('Error stopping message processors:', msg.error);
                                }

                                // Remove this listener
                                worker?.removeListener('message', messageHandler);
                                resolve();
                            }
                        };

                        const errorHandler = (error: Error) => {
                            if (handlerCalled) return;
                            handlerCalled = true;

                            log.error('Worker error during shutdown:', error.message);
                            worker?.removeListener('message', messageHandler);
                            resolve();
                        };

                        const exitHandler = (code: number) => {
                            if (handlerCalled) return;
                            handlerCalled = true;

                            log.info(`Worker exited with code: ${code}`);
                            worker?.removeListener('message', messageHandler);
                            resolve();
                        };

                        // Set up listeners
                        worker?.on('message', messageHandler);
                        worker?.on('error', errorHandler);
                        worker?.on('exit', exitHandler);
                    });

                    // Wait for worker to stop or timeout after 10 seconds
                    const timeoutPromise = new Promise<void>((resolve) => {
                        setTimeout(() => {
                            log.warn('Worker shutdown timeout reached, forcing termination');
                            resolve();
                        }, 10000);
                    });

                    await Promise.race([shutdownPromise, timeoutPromise]);

                    // Force terminate the worker if it's still running
                    if (worker && !worker.threadId) {
                        log.warn('Force terminating worker thread');
                        await worker.terminate();
                    }

                    worker = null;
                    log.info('Worker thread terminated');

                } catch (error) {
                    log.error('Error during worker shutdown:', error instanceof Error ? error.message : String(error));
                    throw error;
                }
            }

            try {
                // Close database connections
                log.info('Closing database connections...');
                
                await Promise.allSettled([
                    MongooseConnector.disconnect(),
                    RedisConnector.disconnect(),
                ]);

                log.info('Database connections closed');
            } catch (error) {
                log.error('Error closing database connections:', error instanceof Error ? error.message : String(error));
            }

            log.info('Message service cleanup completed');
        }

        async function onHealthCheck(): Promise<void> {
            // Add health check logic here
            return Promise.resolve();
        }

        function normalizePort(val: string): number | string | false {
            const port = parseInt(val, 10);
            if (isNaN(port)) return val;
            if (port >= 0) return port;
            return false;
        }

        function onError(error: NodeJS.ErrnoException): void {
            if (error.syscall !== 'listen') {
                throw error;
            }

            const bind = typeof port === 'string' ? 'Pipe ' + port : 'Port ' + port;

            switch (error.code) {
                case 'EACCES':
                    console.error(bind + ' requires elevated privileges');
                    process.exit(1);
                    break;
                case 'EADDRINUSE':
                    console.error(bind + ' is already in use');
                    process.exit(1);
                    break;
                default:
                    throw error;
            }
        }

        function onListening(): void {
            const addr = server.address();
            const bind = typeof addr === 'string' ? 'pipe ' + addr : 'port ' + addr?.port;
            debug('Listening on ' + bind);
            log.info(`Message service listening on ${bind}`);
        }
    })();

} else {
    // Worker thread logic for message processing
    process.on('message', (msg) => {
        if (msg.type === 'shutdown') {
            log.info('Worker received shutdown signal');
            
            // Stop all message processors
            try {
                // Add logic to stop message processing queues here
                process.send?.({ type: 'processors-stopped' });
            } catch (error) {
                process.send?.({ 
                    type: 'processors-shutdown-error', 
                    error: error instanceof Error ? error.message : String(error) 
                });
            }
        }
    });

    process.on('error', (error) => {
        log.error('Worker process error:', error.message);
    });

    process.on('exit', (code) => {
        log.info(`Worker process exiting with code: ${code}`);
    });
}