/**
 * Index router for Message Service
 * Main entry point for all API routes
 */

import { Router, Request, Response } from 'express';

const router = Router();

/**
 * @swagger
 * /api/message/v1/:
 *   get:
 *     summary: Message Service API Information
 *     description: Returns basic information about the Message Service API
 *     tags:
 *       - General
 *     responses:
 *       200:
 *         description: API information
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 service:
 *                   type: string
 *                 version:
 *                   type: string
 *                 description:
 *                   type: string
 *                 endpoints:
 *                   type: array
 *                   items:
 *                     type: string
 */
router.get('/', (req: Request, res: Response) => {
  res.json({
    success: true,
    service: 'shoutout-engage-message-service',
    version: '1.0.0',
    description: 'Message processing microservice for ShoutOut Engage platform',
    endpoints: [
      'GET /health - Health check',
      'POST /messages - Send message',
      'GET /messages/:id - Get message status',
      'GET /providers - List available providers',
      'POST /webhooks/:provider - Webhook endpoints',
      'GET /docs - API documentation'
    ],
    documentation: '/api/message/v1/docs',
    timestamp: new Date().toISOString()
  });
});

export default router;