/**
 * Configuration module for Message Service
 * 
 * This module loads configuration from environment variables and provides
 * a centralized configuration object for the entire application.
 */

import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config();

export interface DatabaseConfig {
  uri: string;
  options?: {
    maxPoolSize?: number;
    serverSelectionTimeoutMS?: number;
    socketTimeoutMS?: number;
  };
}

export interface RedisConfig {
  host: string;
  port: number;
  password?: string;
  db: number;
}

export interface SupabaseConfig {
  url: string;
  anonKey: string;
  serviceRoleKey: string;
}

export interface ApiConfig {
  base_path: string;
  version: string;
}

export interface LoggerConfig {
  name: string;
  level: string;
}

export interface RateLimitConfig {
  windowMs: number;
  maxRequests: number;
}

export interface MessageServiceConfig {
  database: DatabaseConfig;
  redis: RedisConfig;
  supabase: SupabaseConfig;
  api: ApiConfig;
  logger: LoggerConfig;
  rateLimit: RateLimitConfig;
  jwt: {
    secret: string;
    expiresIn: string;
  };
  providers: {
    twilio: {
      accountSid?: string;
      authToken?: string;
    };
    sendgrid: {
      apiKey?: string;
    };
    aws: {
      accessKeyId?: string;
      secretAccessKey?: string;
      region: string;
    };
    mailgun: {
      apiKey?: string;
      domain?: string;
    };
    fcm: {
      serverKey?: string;
    };
  };
  webhook: {
    secret: string;
    baseUrl: string;
  };
  upload: {
    maxFileSize: number;
    path: string;
  };
}

const config: MessageServiceConfig = {
  database: {
    uri: process.env.MONGODB_URI || 'mongodb://localhost:27017/shoutout_engage_message',
    options: {
      maxPoolSize: 10,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000,
    }
  },
  redis: {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || '6379'),
    password: process.env.REDIS_PASSWORD || undefined,
    db: parseInt(process.env.REDIS_DB || '0'),
  },
  supabase: {
    url: process.env.SUPABASE_URL || '',
    anonKey: process.env.SUPABASE_ANON_KEY || '',
    serviceRoleKey: process.env.SUPABASE_SERVICE_ROLE_KEY || '',
  },
  api: {
    base_path: process.env.BASE_PATH || '/api/message/v1',
    version: '1.0.0',
  },
  logger: {
    name: 'MessageService',
    level: process.env.LOG_LEVEL || 'info',
  },
  rateLimit: {
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000'), // 15 minutes
    maxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '1000'),
  },
  jwt: {
    secret: process.env.JWT_SECRET || 'your-jwt-secret',
    expiresIn: process.env.JWT_EXPIRES_IN || '24h',
  },
  providers: {
    twilio: {
      accountSid: process.env.TWILIO_ACCOUNT_SID,
      authToken: process.env.TWILIO_AUTH_TOKEN,
    },
    sendgrid: {
      apiKey: process.env.SENDGRID_API_KEY,
    },
    aws: {
      accessKeyId: process.env.AWS_ACCESS_KEY_ID,
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
      region: process.env.AWS_REGION || 'us-east-1',
    },
    mailgun: {
      apiKey: process.env.MAILGUN_API_KEY,
      domain: process.env.MAILGUN_DOMAIN,
    },
    fcm: {
      serverKey: process.env.FCM_SERVER_KEY,
    },
  },
  webhook: {
    secret: process.env.WEBHOOK_SECRET || 'your-webhook-secret',
    baseUrl: process.env.BASE_WEBHOOK_URL || 'http://localhost:3002/api/message/v1/webhooks',
  },
  upload: {
    maxFileSize: parseInt(process.env.MAX_FILE_SIZE || '********'), // 10MB
    path: process.env.UPLOAD_PATH || './uploads',
  },
};

export default config;