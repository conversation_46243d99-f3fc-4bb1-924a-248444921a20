import { createClient, RedisClientType } from 'redis';
import config from '../../config/index.js';
import { logger } from '../../utils/logger.js';

const log = logger(config.logger);

class RedisConnector {
    private static baseConfig = {
        host: config.redis.host,
        port: config.redis.port,
        password: config.redis.password,
        db: config.redis.db,
    };

    /**
     * Get Redis configuration for different clients
     * @param format - 'standard' for Redis client, 'bull' for Bull MQ
     */
    static getConfig(format: 'standard' | 'bull' = 'standard') {
        if (format === 'bull') {
            return {
                ...this.baseConfig,
                // Bull MQ specific options
                maxRetriesPerRequest: null,
                retryDelayOnFailover: 100,
                enableReadyCheck: false,
                lazyConnect: true,
                maxLoadingTimeout: 0,
            };
        }
        
        return {
            socket: {
                host: this.baseConfig.host,
                port: this.baseConfig.port,
                connectTimeout: 10000,
                lazyConnect: true,
            },
            password: this.baseConfig.password,
            database: this.baseConfig.db,
        };
    }

    /**
     * Initialize Redis connection
     */
    static async initialize(): Promise<void> {
        try {
            await getClient();
            log.info('🟢 Redis connection initialized for Message Service');
        } catch (error) {
            log.error('🔴 Failed to initialize Redis connection:', error);
            throw error;
        }
    }

    /**
     * Get Redis configuration in Bull MQ compatible format
     */
    static getBullConfig() {
        return this.getConfig('bull');
    }

    static async get(key: string): Promise<string | null> {
        const redisClient = await getClient();
        return await redisClient.get(key);
    }

    static async del(...keys: string[]): Promise<number> {
        const redisClient = await getClient();
        return await redisClient.del(keys);
    }

    static async set(key: string, value: string): Promise<string | null> {
        const redisClient = await getClient();
        return await redisClient.set(key, value);
    }

    static async mGet(...keys: string[]): Promise<(string | null)[]> {
        const redisClient = await getClient();
        return await redisClient.mGet(keys);
    }

    static async mSet(kvPairs: Record<string, string>): Promise<'OK'> {
        const redisClient = await getClient();
        const flatPairs = Object.entries(kvPairs).flat();
        return await redisClient.mSet(flatPairs as string[]);
    }

    static async expire(key: string, seconds: number): Promise<number> {
        const redisClient = await getClient();
        return await redisClient.expire(key, seconds);
    }

    static async scanAndDelete(pattern: string): Promise<void> {
        try {
            const redisClient = await getClient();
            let cursor: string = "0";
            do {
                const reply = await redisClient.scan(cursor, {
                    MATCH: pattern,
                    COUNT: 1000,
                });
                cursor = String(reply.cursor);
                const keys = reply.keys;
                if (keys.length > 0) await redisClient.del(keys);
            } while (cursor !== '0');
        } catch (e) {
            log.error('Error in scanAndDelete:', e);
        }
    }

    /**
     * Check if Redis is connected and responding
     */
    static async ping(): Promise<boolean> {
        try {
            const redisClient = await getClient();
            const result = await redisClient.ping();
            return result === 'PONG';
        } catch (error) {
            log.error('Redis ping failed:', error);
            return false;
        }
    }

    /**
     * Disconnect from Redis
     */
    static async disconnect(): Promise<void> {
        try {
            if (client && client.isReady) {
                await client.quit();
                client = null;
                log.info('🟡 Redis disconnected for Message Service');
            }
        } catch (err) {
            log.error('🔴 Error disconnecting from Redis:', err);
            throw err;
        }
    }

    /**
     * Close Redis connection (alias for disconnect)
     */
    static async closeConnection(): Promise<void> {
        return this.disconnect();
    }
}

// Create Redis client using the unified config
const redisClientOptions = RedisConnector.getConfig('standard');
let client: RedisClientType | null = null;
let isConnecting = false;

async function getClient(): Promise<RedisClientType> {
    if (client && client.isReady) {
        return client;
    }

    if (isConnecting) {
        // Wait for existing connection attempt
        while (isConnecting) {
            await new Promise(resolve => setTimeout(resolve, 100));
        }
        if (client && client.isReady) {
            return client;
        }
    }

    isConnecting = true;
    try {
        if (!client) {
            client = createClient(redisClientOptions);
            client.on('error', (err: any) => log.error('Redis Client Error', err));
        }

        if (!client.isReady) {
            await client.connect();
            log.info('🟢 Redis client connected successfully');
        }

        return client;
    } catch (err) {
        log.error('🔴 Failed to connect to Redis:', err);
        throw err;
    } finally {
        isConnecting = false;
    }
}

export default RedisConnector;