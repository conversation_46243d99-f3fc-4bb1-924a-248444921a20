import { OAS3Options } from 'swagger-jsdoc';
import config from '../config/index.js';

class Swagger {
    static getOptions(): OAS3Options {
        return {
            definition: {
                openapi: '3.0.0',
                info: {
                    title: 'ShoutOUT Engage Message Service',
                    version: '1.0.0',
                    description:
                        'Message processing microservice for ShoutOut Engage platform. Handles SMS, Email, and Push notifications through multiple providers.',
                    contact: {
                        name: 'ShoutOUT',
                        url: 'https://getshoutout.com/',
                        email: '<EMAIL>',
                    },
                },
                servers: [
                    {
                        url: process.env.SWAGGER_SERVER_URL || `http://localhost:3002${config.api.base_path}`,
                        description: 'Message Service API Server',
                    },
                ],
                components: {
                    securitySchemes: {
                        BearerAuth: {
                            type: 'http',
                            scheme: 'bearer',
                            bearerFormat: 'JWT',
                            description: 'Supabase JWT token for user authentication',
                        },
                        ApiKey: {
                            type: 'api<PERSON>ey',
                            in: 'header',
                            name: 'x-api-key',
                            description: 'API key for service-to-service communication',
                        },
                    },
                    schemas: {
                        MessageRequest: {
                            type: 'object',
                            required: ['transport', 'from', 'to', 'content'],
                            properties: {
                                transport: {
                                    type: 'string',
                                    enum: ['SMS', 'EMAIL', 'PUSH', 'FACEBOOK_MESSENGER'],
                                    description: 'Message transport type',
                                },
                                from: {
                                    type: 'string',
                                    description: 'Sender address/number',
                                },
                                to: {
                                    type: 'string',
                                    description: 'Recipient address/number',
                                },
                                content: {
                                    type: 'string',
                                    description: 'Message content',
                                },
                                subject: {
                                    type: 'string',
                                    description: 'Message subject (for email)',
                                },
                                htmlContent: {
                                    type: 'string',
                                    description: 'HTML content (for email)',
                                },
                                campaignId: {
                                    type: 'string',
                                    description: 'Associated campaign ID',
                                },
                            },
                        },
                        MessageResponse: {
                            type: 'object',
                            properties: {
                                success: {
                                    type: 'boolean',
                                },
                                messageId: {
                                    type: 'string',
                                },
                                providerId: {
                                    type: 'string',
                                },
                                providerMessageId: {
                                    type: 'string',
                                },
                                cost: {
                                    type: 'number',
                                },
                                currency: {
                                    type: 'string',
                                },
                            },
                        },
                        ErrorResponse: {
                            type: 'object',
                            properties: {
                                success: {
                                    type: 'boolean',
                                    example: false,
                                },
                                error: {
                                    type: 'object',
                                    properties: {
                                        code: {
                                            type: 'string',
                                        },
                                        message: {
                                            type: 'string',
                                        },
                                        requestId: {
                                            type: 'string',
                                        },
                                    },
                                },
                            },
                        },
                    },
                },
                security: [
                    {
                        BearerAuth: [],
                    },
                ],
            },
            apis: ['./src/routes/*.ts'],
        };
    }
}

export default Swagger;