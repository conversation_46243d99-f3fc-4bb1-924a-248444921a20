/**
 * Error Middleware for Message Service
 * Centralized error handling for the entire application
 */

import { Request, Response, NextFunction } from 'express';
import { logger } from '@/lib/utils/logger.js';

const log = logger({ name: 'ErrorMiddleware', level: 'error' });

export class ErrorMiddleware {
    /**
     * Handle errors and send appropriate responses
     */
    static handleError = (error: any, req: Request, res: Response, next: NextFunction): void => {
        try {
            // Log the error
            log.error('Error occurred:', {
                error: error.message,
                stack: error.stack,
                requestId: req.requestId,
                method: req.method,
                url: req.url,
                userAgent: req.get('User-Agent'),
                ip: req.ip
            });

            // Default error response
            const errorResponse = {
                success: false,
                error: {
                    code: error.code || 'INTERNAL_ERROR',
                    message: error.message || 'An internal server error occurred',
                    requestId: req.requestId
                }
            };

            // Determine status code
            let statusCode = 500;
            if (error.statusCode) {
                statusCode = error.statusCode;
            } else if (error.status) {
                statusCode = error.status;
            }

            // Handle specific error types
            if (error.name === 'ValidationError') {
                statusCode = 422;
                errorResponse.error.code = 'VALIDATION_ERROR';
            } else if (error.name === 'UnauthorizedError') {
                statusCode = 401;
                errorResponse.error.code = 'UNAUTHORIZED';
            } else if (error.name === 'ForbiddenError') {
                statusCode = 403;
                errorResponse.error.code = 'FORBIDDEN';
            } else if (error.name === 'NotFoundError') {
                statusCode = 404;
                errorResponse.error.code = 'NOT_FOUND';
            } else if (error.name === 'ConflictError') {
                statusCode = 409;
                errorResponse.error.code = 'CONFLICT';
            }

            // In development, include stack trace
            if (process.env.NODE_ENV === 'development') {
                (errorResponse as any).stack = error.stack;
            }

            res.status(statusCode).json(errorResponse);
        } catch (handlingError) {
            // If error handling fails, send a basic response
            log.error('Error in error handler:', handlingError);
            res.status(500).json({
                success: false,
                error: {
                    code: 'INTERNAL_ERROR',
                    message: 'An internal server error occurred',
                    requestId: req.requestId || 'unknown'
                }
            });
        }
    };
}