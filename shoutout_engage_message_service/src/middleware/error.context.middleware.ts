/**
 * Error Context Middleware for Message Service
 * Captures request context for error logging and tracking
 */

import { Request, Response, NextFunction } from 'express';
import { v4 as uuidv4 } from 'uuid';

declare global {
    namespace Express {
        interface Request {
            requestId: string;
            errorContext?: any;
            user?: {
                id: string;
                organizationId?: string;
                [key: string]: any;
            };
        }
    }
}

export interface RequestWithErrorContext extends Request {
    requestId: string;
    errorContext?: any;
}

export class ErrorContextMiddleware {
    /**
     * Capture and store request context for error handling
     */
    static captureContext = (req: Request, res: Response, next: NextFunction): void => {
        try {
            // Generate unique request ID if not present
            if (!req.requestId) {
                req.requestId = uuidv4();
            }

            // Create basic error context
            req.errorContext = {
                requestId: req.requestId,
                method: req.method,
                url: req.url,
                userAgent: req.get('User-Agent'),
                ip: req.ip,
                timestamp: new Date().toISOString(),
                user: req.user || null
            };

            // Add request ID to response headers for debugging
            res.setHeader('X-Request-ID', req.requestId);

            next();
        } catch (error) {
            console.error('Failed to capture error context:', error);
            // Ensure we have at least a request ID
            if (!req.requestId) {
                req.requestId = uuidv4();
            }
            next();
        }
    };

    /**
     * Get existing error context from request
     */
    static getContext = (req: Request): any => {
        return req.errorContext || null;
    };

    /**
     * Ensure error context exists, create if missing
     */
    static ensureContext = (req: Request): any => {
        if (!req.errorContext) {
            if (!req.requestId) {
                req.requestId = uuidv4();
            }
            req.errorContext = {
                requestId: req.requestId,
                method: req.method,
                url: req.url,
                timestamp: new Date().toISOString()
            };
        }
        return req.errorContext;
    };

    /**
     * Create middleware to add additional context
     */
    static withAdditionalContext = (contextExtractor: (req: Request) => any) => {
        return (req: Request, res: Response, next: NextFunction) => {
            try {
                const context = ErrorContextMiddleware.ensureContext(req);
                const additionalContext = contextExtractor(req);
                Object.assign(context, additionalContext);
                next();
            } catch (error) {
                console.error('Failed to add additional context:', error);
                next();
            }
        };
    };
}

// Export convenience functions
export const captureErrorContext = ErrorContextMiddleware.captureContext;
export const getErrorContext = ErrorContextMiddleware.getContext;
export const ensureErrorContext = ErrorContextMiddleware.ensureContext;
export const withAdditionalContext = ErrorContextMiddleware.withAdditionalContext;