# Server Configuration
NODE_ENV=development
PORT=3002
BASE_PATH=/api/message/v1

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/shoutout_engage_message
MONGODB_TEST_URI=mongodb://localhost:27017/shoutout_engage_message_test

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# Supabase Configuration
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# JWT Configuration
JWT_SECRET=your-jwt-secret
JWT_EXPIRES_IN=24h

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000

# SMS Providers
TWILIO_ACCOUNT_SID=your-twilio-sid
TWILIO_AUTH_TOKEN=your-twilio-token
NEXMO_API_KEY=your-nexmo-key
NEXMO_API_SECRET=your-nexmo-secret
DIALOG_API_KEY=your********key

# Email Providers
SENDGRID_API_KEY=your-sendgrid-key
AWS_ACCESS_KEY_ID=your-aws-key
AWS_SECRET_ACCESS_KEY=your-aws-secret
AWS_REGION=us-east-1
MAILGUN_API_KEY=your-mailgun-key
MAILGUN_DOMAIN=your-mailgun-domain

# Push Notification Providers
FCM_SERVER_KEY=your-fcm-key
APNS_KEY_ID=your-apns-key-id
APNS_TEAM_ID=your-apns-team-id
APNS_BUNDLE_ID=your-bundle-id

# Webhook Configuration
WEBHOOK_SECRET=your-webhook-secret
BASE_WEBHOOK_URL=https://your-domain.com/api/message/v1/webhooks

# External Services
CAMPAIGN_SERVICE_URL=http://localhost:3001
PAYMENT_SERVICE_URL=http://localhost:3003
CORE_SERVICE_URL=http://localhost:3000

# Queue Configuration
QUEUE_REDIS_HOST=localhost
QUEUE_REDIS_PORT=6379
QUEUE_REDIS_DB=1

# Internal API Key for service-to-service communication
INTERNAL_API_KEY=your-internal-api-key

# Logging
LOG_LEVEL=info
LOG_FILE=./logs/message-service.log

# File Upload
MAX_FILE_SIZE=10485760
UPLOAD_PATH=./uploads